/**
 * Admin Custom Fields Synchronization Handler
 *
 * HTTP handler for bidirectional custom field value synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Provides admin-level access
 * to patient custom field synchronization with comprehensive validation,
 * error handling, and response formatting.
 *
 * **Endpoint:** `POST /admin/custom-fields-sync/:id/:platform`
 * - `:id` = local database patient ID (string, validated as non-empty)
 * - `:platform` = target platform ("ap" or "cc", enum validation required)
 *
 * **Query Parameters:**
 * - `skip=true`: Skip processing for missing custom fields (optional)
 * - `return=true`: Indicates callback from /cf endpoint (optional)
 *
 * **Features:**
 * - Comprehensive parameter validation
 * - Platform enum validation
 * - Request ID correlation and logging
 * - Detailed error responses with context
 * - Success responses with synchronization statistics
 *
 * @fileoverview Admin endpoint for patient custom field value synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { Context } from "hono";
import {
	syncApToCcCustomFields,
	syncCcToApCustomFields,
} from "@/processors/patientCustomFields";
import type {
	PatientCustomFieldSyncResult,
	Platform,
} from "@/processors/patientCustomFields/types";
import { logError, logInfo, logWarn } from "@/utils/logger";
import { useProcess } from "@/processors/Process";

/**
 * Handle admin custom fields synchronization request
 *
 * Processes bidirectional custom field value synchronization requests for
 * specific patients. Validates parameters, determines sync direction, and
 * executes appropriate synchronization method with comprehensive error handling.
 *
 * **Request Validation:**
 * - Patient ID must be non-empty string
 * - Platform must be "ap" or "cc"
 * - Query parameters are optional but validated if present
 *
 * **Synchronization Logic:**
 * - platform="ap": Sync FROM CliniCore TO AutoPatient
 * - platform="cc": Sync FROM AutoPatient TO CliniCore
 * - Follows exact sequence from requirements with webhook loop prevention
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with synchronization results
 *
 * @example
 * ```typescript
 * // Used in Hono route definition
 * app.post('/admin/custom-fields-sync/:id/:platform', adminCustomFieldsSyncHandler);
 *
 * // Success response (200):
 * {
 *   "status": "success",
 *   "message": "Custom field synchronization completed",
 *   "data": {
 *     "patientId": "patient-456",
 *     "targetPlatform": "cc",
 *     "fieldsProcessed": 5,
 *     "success": true,
 *     "errors": []
 *   },
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "skipMissingFields": true,
 *     "isReturnCallback": false
 *   }
 * }
 *
 * // Error response (400/404/500):
 * {
 *   "status": "error",
 *   "message": "Validation failed",
 *   "error": "Invalid platform parameter",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z"
 *   }
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function adminCustomFieldsSyncHandler(
	c: Context,
): Promise<Response> {
	const timestamp = new Date().toISOString();
	let sourceId: string | number |undefined;

	try {
		// Extract and validate path parameters
		const patientId = c.req.param("id");
		const platform = c.req.param("platform") as Platform;

		// Validate patient ID
		if (!patientId || patientId.trim().length === 0) {
			logWarn("Invalid patient ID parameter", { patientId });
			return c.json(
				{
					status: "error",
					message: "Validation failed",
					error: "Patient ID parameter is required and must be non-empty",
					metadata: { timestamp },
				},
				400,
			);
		}

		sourceId = patientId;

		// Validate platform parameter
		if (!platform || !["ap", "cc"].includes(platform)) {
			logWarn("Invalid platform parameter", { platform });
			return c.json(
				{
					status: "error",
					message: "Validation failed",
					error: "Platform parameter must be 'ap' or 'cc'",
					metadata: { timestamp },
				},
				400,
			);
		}

		const process = await useProcess().reteriveContext(patientId, "patient");
		await process.inProcess(patientId, "patient");

		// Extract and validate query parameters
		const skipParam = c.req.query("skip");
		const returnParam = c.req.query("return");

		const skipMissingFields = skipParam === "true" || returnParam === "true"; // Auto-enable skip for return callbacks
		const isReturnCallback = returnParam === "true";

		// Execute synchronization based on target platform
		let syncResult: PatientCustomFieldSyncResult;
		if (platform === "cc") {
			syncResult = await syncApToCcCustomFields(
				patientId.trim(),
				skipMissingFields,
			);
		} else {
			syncResult = await syncCcToApCustomFields(
				patientId.trim(),
				skipMissingFields,
			);
		}

		// Determine response status based on sync result
		const responseStatus = syncResult.success ? 200 : 500;
		const statusMessage = syncResult.success ? "success" : "error";
		const message = syncResult.success
			? "Custom field synchronization completed"
			: "Custom field synchronization failed";

		// Log final result
		if (syncResult.success) {
			await process.complete();
			logInfo("Admin custom fields sync completed successfully", {
				patientId: patientId.trim(),
				platform,
				fieldsProcessed: syncResult.fieldsProcessed,
				errorCount: syncResult.errors.length,
			});
		} else {
			await process.failed(syncResult.errors.join("; "));
			logError("Admin custom fields sync failed", {
				patientId: patientId.trim(),
				platform,
				fieldsProcessed: syncResult.fieldsProcessed,
				errors: syncResult.errors,
			});
		}

		// Return comprehensive response
		return c.json(
			{
				status: statusMessage,
				message,
				data: {
					patientId: patientId.trim(),
					targetPlatform: platform,
					fieldsProcessed: syncResult.fieldsProcessed,
					success: syncResult.success,
					errors: syncResult.errors,
				},
				metadata: {
					timestamp,
					skipMissingFields,
					isReturnCallback,
				},
			},
			responseStatus,
		);
	} catch (error) {
		const errorMessage = "Admin custom fields sync handler failed";
		logError(errorMessage, { error });

		if (sourceId) {
			const process = await useProcess().reteriveContext(sourceId, "patient");
			await process.tryAgain(errorMessage);
		}

		return c.json(
			{
				status: "error",
				message: errorMessage,
				error: String(error),
				metadata: { timestamp },
			},
			500,
		);
	}
}

/**
 * Validate admin custom fields sync request parameters
 *
 * Helper function to validate request parameters for the admin custom fields
 * sync endpoint. Provides detailed validation with specific error messages.
 *
 * @param patientId - Patient ID from path parameter
 * @param platform - Platform from path parameter
 * @returns Validation result with success status and error message
 *
 * @since 1.0.0
 */
export function validateSyncRequest(
	patientId: string | undefined,
	platform: string | undefined,
): { valid: boolean; error?: string } {
	// Validate patient ID
	if (!patientId || patientId.trim().length === 0) {
		logWarn("Patient ID validation failed", { patientId });
		return {
			valid: false,
			error: "Patient ID parameter is required and must be non-empty",
		};
	}

	// Validate platform
	if (!platform || !["ap", "cc"].includes(platform)) {
		logWarn("Platform validation failed", { platform });
		return {
			valid: false,
			error: "Platform parameter must be 'ap' or 'cc'",
		};
	}

	return { valid: true };
}

/**
 * Format sync response for consistent API responses
 *
 * Helper function to format synchronization responses with consistent
 * structure and comprehensive metadata.
 *
 * @param syncResult - Synchronization result from sync methods
 * @param patientId - Patient ID for response context
 * @param platform - Target platform for response context
 * @param metadata - Additional metadata for response
 * @returns Formatted response object
 *
 * @since 1.0.0
 */
export function formatSyncResponse(
	syncResult: { success: boolean; fieldsProcessed: number; errors: string[] },
	patientId: string,
	platform: Platform,
	metadata: Record<string, string | number | boolean> = {},
) {
	const timestamp = new Date().toISOString();
	const statusMessage = syncResult.success ? "success" : "error";
	const message = syncResult.success
		? "Custom field synchronization completed"
		: "Custom field synchronization failed";

	return {
		status: statusMessage,
		message,
		data: {
			patientId,
			targetPlatform: platform,
			fieldsProcessed: syncResult.fieldsProcessed,
			success: syncResult.success,
			errors: syncResult.errors,
		},
		metadata: {
			timestamp,
			...metadata,
		},
	};
}
